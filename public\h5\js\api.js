// function addScript(url){
//     document.write("<script language=javascript src="+url+"></script>");
// }
// addScript('js/jquery.min.js');
// addScript('js/vue.js');

// 接口路径
// var bathUrl = 'http://ice.sdbaocheng.com/api/';
var bathUrl = 'http://ice.sdbaocheng.com/RsbnaxDptB.php/';
// 获取缓存
var storage = window.localStorage;
let token = storage.getItem("token") || "";
// 发送请求
function request(url,params){
	var res = null
	// 如果有token 就存token
	if(token){
		params.token = token;
	}
	// 公共参数
	$.ajax({
		url: bathUrl + url, //请求的url地址
		dataType: "json", //返回的格式为json
		async: false, //请求是否异步，默认true异步，这是ajax的特性
		// data:JSON.stringify(params), //参数值
		data: params, //参数值
		// traditional:true,
		type: "GET", //请求的方式
		headers: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
		success: function (data) {//请求成功的处理
			toLogin(data)
			res = data
		},
		error: function (err) {//请求出错的处理
			toLogin(err)
			res = err
		}
	});
	return res
}

// url 路由  isLogin 是否登录拦截
function getUrl(url, isLogin) {
	if (!isLogin) {
		window.location.href = url
	}
}

function goBack() {
	setTimeout(()=>{
		window.history.go(-1);
	},1000);
}

//截取字符串
function GetRequest() {
	var url = location.search; //获取url中"?"符后的字串
	var theRequest = new Object();
	if (url.indexOf("?") != -1) {
		var str = url.substr(1);
		strs = str.split("&");
		for(var i = 0; i < strs.length; i ++) {
			theRequest[strs[i].split("=")[0]]=unescape(strs[i].split("=")[1]);
		}
	}
	return theRequest;
}

// 设置换缓存
function setStorage(key, value) {
	storage.setItem(key, JSON.stringify(value));
}
// 获取缓存
function getStorage(key) {
	var v = storage.getItem(key)
	if (v == null || v == "{}") {
		return v = ""
	}
	return JSON.parse(v)
}

// 登录拦截
function toLogin(err) {
	if (err.code == 401) {
		// storage.clear(); // 清除缓存
		localStorage.clear('user');
		window.location.href = '/login.html'
	}
}

// 管理路由
function goUrl(e) {
	window.location.href = e
}

